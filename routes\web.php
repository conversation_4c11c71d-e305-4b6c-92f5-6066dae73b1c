<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ParishController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\EntryController;
use App\Http\Controllers\PaymentController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('register');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::resource('parishes', ParishController::class);
    Route::resource('parishes.categories', CategoryController::class);
    Route::resource('entries', EntryController::class);
    Route::get('entries/{entry}/download', [EntryController::class, 'download'])->name('entries.download');
    Route::get('categories/{category}/fields', [EntryController::class, 'fields'])->name('categories.fields');

    Route::get('payments', [PaymentController::class, 'index'])->name('payments.index');
});

require __DIR__.'/auth.php';
