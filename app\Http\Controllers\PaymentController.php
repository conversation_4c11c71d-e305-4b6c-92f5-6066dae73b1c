<?php

namespace App\Http\Controllers;

use App\Models\Entry;
use App\Models\Parish;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PaymentController extends Controller
{
    public function index(Request $request): View
    {
        $user = $request->user();
        $parishId = $request->input('parish_id', $user->parish_id);
        $period = $request->input('period', 'monthly'); // monthly|quarterly
        $year = (int)$request->input('year', now()->year);
        $month = (int)$request->input('month', now()->month);
        $quarter = (int)$request->input('quarter', (int)ceil($month / 3));

        $parish = Parish::findOrFail($parishId);

        $query = Entry::where('parish_id', $parish->id);
        if ($period === 'monthly') {
            $start = Carbon::create($year, $month, 1);
            $end = (clone $start)->endOfMonth();
        } else { // quarterly
            $start = Carbon::create($year, ($quarter - 1) * 3 + 1, 1)->startOfMonth();
            $end = (clone $start)->addMonths(2)->endOfMonth();
        }
        $query->whereBetween('start_date', [$start->toDateString(), $end->toDateString()]);

        $weeks = $query->count();
        $amount = 0;
        if ($parish->enable_payment_tracking && $parish->weekly_payment_amount) {
            $amount = $weeks * (float)$parish->weekly_payment_amount;
        }

        return view('payments.index', [
            'parish' => $parish,
            'period' => $period,
            'weeks' => $weeks,
            'amount' => $amount,
            'year' => $year,
            'month' => $month,
            'quarter' => $quarter,
        ]);
    }
}
