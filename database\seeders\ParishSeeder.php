<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Parish;
use App\Models\Category;

class ParishSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $parish = Parish::create([
            'name' => 'Paroisse Saint Michel',
            'enable_payment_tracking' => true,
            'weekly_payment_amount' => 1000,
            'storage_path' => 'public',
        ]);

        $defaultCategories = [
            'Quête dominicale',
            'Dîmes',
            'Offrandes spéciales',
            'Dons divers',
        ];

        foreach ($defaultCategories as $name) {
            Category::create([
                'parish_id' => $parish->id,
                'name' => $name,
                'template_file' => 'templates/sample.docx',
            ]);
        }
    }
}
