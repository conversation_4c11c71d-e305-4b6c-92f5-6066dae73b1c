<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">Modifier paroisse</h2>
    </x-slot>
    <div class="py-6">
        <div class="max-w-3xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white shadow sm:rounded-lg p-6">
                <form method="POST" action="{{ route('parishes.update', $parish) }}" class="space-y-4">
                    @csrf
                    @method('PUT')
                    <div>
                        <label class="block text-sm font-medium">Nom</label>
                        <input name="name" class="mt-1 w-full border rounded p-2" value="{{ $parish->name }}" required />
                    </div>
                    <div class="flex items-center space-x-2">
                        <input type="checkbox" name="enable_payment_tracking" value="1" {{ $parish->enable_payment_tracking ? 'checked' : '' }} />
                        <label>Activer le suivi de paie</label>
                    </div>
                    <div>
                        <label class="block text-sm font-medium">Montant hebdomadaire</label>
                        <input name="weekly_payment_amount" type="number" step="0.01" class="mt-1 w-full border rounded p-2" value="{{ $parish->weekly_payment_amount }}" />
                    </div>
                    <div>
                        <label class="block text-sm font-medium">Dossier de stockage</label>
                        <input name="storage_path" class="mt-1 w-full border rounded p-2" value="{{ $parish->storage_path }}" />
                    </div>
                    <div class="flex justify-end space-x-2">
                        <a href="{{ route('parishes.index') }}" class="px-4 py-2 border rounded">Annuler</a>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded">Enregistrer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>



