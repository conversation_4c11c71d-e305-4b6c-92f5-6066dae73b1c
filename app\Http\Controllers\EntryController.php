<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Entry;
use App\Models\Parish;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use PhpOffice\PhpWord\TemplateProcessor;
use App\Services\WordDocumentService;

class EntryController extends Controller
{
    public function index(Request $request): View
    {
        $user = $request->user();
        $query = Entry::with(['category', 'parish'])->latest();
        if ($user->role === 'user' && $user->parish_id) {
            $query->where('parish_id', $user->parish_id);
        }
        $month = (int)$request->input('month', 0);
        $quarter = (int)$request->input('quarter', 0);
        $year = (int)$request->input('year', 0);
        if ($year > 0) {
            $query->whereYear('start_date', $year);
        }
        if ($month > 0) {
            $query->whereMonth('start_date', $month);
        } elseif ($quarter > 0) {
            $months = range(($quarter - 1) * 3 + 1, ($quarter - 1) * 3 + 3);
            $query->whereIn(\Illuminate\Support\Facades\DB::raw('MONTH(start_date)'), $months);
        }
        $entries = $query->paginate(15)->appends($request->only('month','quarter','year'));
        return view('entries.index', compact('entries', 'month', 'quarter', 'year'));
    }


    public function create(Request $request): View
    {
        $user = $request->user();
        $parishes = Parish::orderBy('name')->get();
        $categories = Category::orderBy('name')->get();
        if ($user->role === 'user' && $user->parish_id) {
            $parishes = Parish::where('id', $user->parish_id)->get();
            $categories = Category::where('parish_id', $user->parish_id)->orderBy('name')->get();
        }
        return view('entries.create', compact('parishes', 'categories'));
    }

    public function store(Request $request): RedirectResponse
    {
        $user = $request->user();
        $validated = $request->validate([
            'parish_id' => ['required', 'exists:parishes,id'],
            'category_id' => ['required', 'exists:categories,id'],
            'week_label' => ['required', 'string', 'max:255'],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after_or_equal:start_date'],
            'data' => ['required_without:document', 'array'],
            'document' => ['nullable', 'file', 'mimes:doc,docx', 'max:10240'],
        ]);
        if ($user->role !== 'admin' && $user->parish_id != $validated['parish_id']) {
            abort(403);
        }
        $parish = Parish::findOrFail($validated['parish_id']);
        $category = Category::findOrFail($validated['category_id']);
        if ($user->role !== 'admin' && $category->parish_id != $user->parish_id) {
            abort(403);
        }

        $entry = Entry::create([
            'parish_id' => $parish->id,
            'category_id' => $category->id,
            'week_label' => $validated['week_label'],
            'start_date' => $validated['start_date'],
            'end_date' => $validated['end_date'],
            'data_json' => $validated['data'] ?? [],
        ]);

        $start = \Carbon\Carbon::parse($validated['start_date']);
        $month = $start->locale('fr')->translatedFormat('F');
        $month = ucfirst($month);
        $weekLabel = $validated['week_label'];
        $quarter = 'trimestre_' . (int)ceil($start->quarter);
        $categoryFolder = 'categorie_' . $category->id;

        $base = $parish->getStorageBasePath();
        $dir = $base . '/paroisse_' . $parish->id . '/' . $quarter . '/' . $categoryFolder;
        if ($parish->isAbsoluteStorageBase()) {
            // When absolute base, write using local filesystem adapter via putFileAs/put with full path
            if (!is_dir(storage_path('app'))) {
                // ensure storage/app exists
                @mkdir(storage_path('app'), 0777, true);
            }
            @mkdir($dir, 0777, true);
        } else {
            Storage::makeDirectory($dir);
        }

        $filename = str_replace(' ', '_', $month . '_' . $weekLabel) . '.docx';
        $relativePath = $dir . '/' . $filename;

        // Si un fichier a été téléchargé, l'utiliser directement
        if ($request->hasFile('document')) {
            $uploadedFile = $request->file('document');
            if ($parish->isAbsoluteStorageBase()) {
                $relativePath = rtrim($dir, '/').'/'.$filename;
                $uploadedFile->move($dir, $filename);
            } else {
                $relativePath = Storage::putFileAs($dir, $uploadedFile, $filename);
            }
            $entry->update(['generated_file' => $relativePath]);
            return redirect()->route('entries.index')->with('status', 'Fiche téléchargée avec succès.');
        } 
        // Sinon, générer le document via template s'il existe, sinon via preset
        else {
            if (!empty($category->template_file)) {
                $templatePath = storage_path('app/' . $category->template_file);
                $processor = new TemplateProcessor($templatePath);
                foreach (($validated['data'] ?? []) as $key => $value) {
                    $processor->setValue($key, (string)$value);
                }

                $tempFile = tempnam(sys_get_temp_dir(), 'pc_') . '.docx';
                $processor->saveAs($tempFile);
                if ($parish->isAbsoluteStorageBase()) {
                    $relativePath = rtrim($dir, '/').'/'.$filename;
                    @copy($tempFile, $relativePath);
                } else {
                    Storage::put($relativePath, file_get_contents($tempFile));
                }
            } else {
                $service = app(WordDocumentService::class);
                $relativePath = $service->generateFromPreset($entry, $category);
            }

            $entry->update(['generated_file' => $relativePath]);
            return redirect()->route('entries.index')->with('status', 'Fiche générée automatiquement.');
        }
    }

    public function show(Entry $entry): View
    {
        return view('entries.show', compact('entry'));
    }

    public function edit(Entry $entry): View
    {
        $user = request()->user();
        abort_unless($user->role === 'admin' || $user->parish_id === $entry->parish_id, 403);
        $parishes = Parish::orderBy('name')->get();
        $categories = Category::where('parish_id', $entry->parish_id)->orderBy('name')->get();
        return view('entries.edit', compact('entry', 'parishes', 'categories'));
    }

    public function update(Request $request, Entry $entry): RedirectResponse
    {
        $user = $request->user();
        $validated = $request->validate([
            'parish_id' => ['required', 'exists:parishes,id'],
            'category_id' => ['required', 'exists:categories,id'],
            'week_label' => ['required', 'string', 'max:255'],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after_or_equal:start_date'],
            'data' => ['required', 'array'],
        ]);
        if ($user->role !== 'admin' && $user->parish_id != $validated['parish_id']) {
            abort(403);
        }

        $entry->update([
            'parish_id' => $validated['parish_id'],
            'category_id' => $validated['category_id'],
            'week_label' => $validated['week_label'],
            'start_date' => $validated['start_date'],
            'end_date' => $validated['end_date'],
            'data_json' => $validated['data'],
        ]);

        return redirect()->route('entries.index')->with('status', 'Fiche mise à jour.');
    }

    public function destroy(Entry $entry): RedirectResponse
    {
        $user = request()->user();
        abort_unless($user->role === 'admin' || $user->parish_id === $entry->parish_id, 403);
        $entry->delete();
        return redirect()->route('entries.index')->with('status', 'Fiche supprimée.');
    }

    public function download(Entry $entry)
    {
        abort_unless($entry->generated_file, 404);
        $path = $entry->generated_file;
        if ($entry->parish && $entry->parish->isAbsoluteStorageBase()) {
            abort_unless(is_file($path), 404);
            return response()->download($path);
        }
        abort_unless(Storage::exists($path), 404);
        return Storage::download($path);
    }

    public function fields(Category $category)
    {
        return response()->json($category->fields_json ?? []);
    }
}
