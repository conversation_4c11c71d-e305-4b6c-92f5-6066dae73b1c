<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Tableau de bord') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Statistiques générales -->
            <div id="dashboard-stats" class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900">
                    <h3 class="text-lg font-semibold mb-4">Statistiques générales</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-blue-100 p-4 rounded-lg shadow">
                            <h4 class="text-sm font-medium text-blue-800">Total des paiements</h4>
                            <p id="total-payments" class="text-2xl font-bold text-blue-600">Chargement...</p>
                        </div>
                        <div class="bg-green-100 p-4 rounded-lg shadow">
                            <h4 class="text-sm font-medium text-green-800">Nombre de paroisses</h4>
                            <p id="parishes-count" class="text-2xl font-bold text-green-600">Chargement...</p>
                        </div>
                        <div class="bg-purple-100 p-4 rounded-lg shadow">
                            <h4 class="text-sm font-medium text-purple-800">Moyenne par paroisse</h4>
                            <p id="average-per-parish" class="text-2xl font-bold text-purple-600">Chargement...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Paroisses récentes -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Paroisses récentes</h3>
                        <a href="{{ route('parishes.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Voir toutes
                        </a>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="data-table min-w-full bg-white" data-endpoint="/api/parishes" data-resource="parishes" data-actions="true" data-columns='[{"field":"name","label":"Nom"},{"field":"enable_payment_tracking","label":"Suivi paiements"},{"field":"weekly_payment_amount","label":"Montant hebdomadaire"}]'>
                            <thead>
                                <tr>
                                    <th class="px-4 py-2 border">Nom</th>
                                    <th class="px-4 py-2 border">Suivi paiements</th>
                                    <th class="px-4 py-2 border">Montant hebdomadaire</th>
                                    <th class="px-4 py-2 border">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="4" class="px-4 py-2 border text-center">Chargement des données...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Entrées récentes -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Entrées récentes</h3>
                        <a href="{{ route('entries.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Voir toutes
                        </a>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="data-table min-w-full bg-white" data-endpoint="/api/entries" data-resource="entries" data-actions="true" data-columns='[{"field":"parish.name","label":"Paroisse"},{"field":"category.name","label":"Catégorie"},{"field":"week_number","label":"Semaine"},{"field":"created_at","label":"Date"}]'>
                            <thead>
                                <tr>
                                    <th class="px-4 py-2 border">Paroisse</th>
                                    <th class="px-4 py-2 border">Catégorie</th>
                                    <th class="px-4 py-2 border">Semaine</th>
                                    <th class="px-4 py-2 border">Date</th>
                                    <th class="px-4 py-2 border">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="5" class="px-4 py-2 border text-center">Chargement des données...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Paiements récents -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold">Paiements récents</h3>
                        <a href="{{ route('payments.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Voir tous
                        </a>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="data-table min-w-full bg-white" data-endpoint="/api/payments" data-resource="payments" data-actions="true" data-columns='[{"field":"parish.name","label":"Paroisse"},{"field":"amount","label":"Montant"},{"field":"payment_date","label":"Date"},{"field":"notes","label":"Notes"}]'>
                            <thead>
                                <tr>
                                    <th class="px-4 py-2 border">Paroisse</th>
                                    <th class="px-4 py-2 border">Montant</th>
                                    <th class="px-4 py-2 border">Date</th>
                                    <th class="px-4 py-2 border">Notes</th>
                                    <th class="px-4 py-2 border">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="5" class="px-4 py-2 border text-center">Chargement des données...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chargement de Chart.js pour les graphiques -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Fonction pour initialiser les graphiques
        function initCharts(stats) {
            // Cette fonction sera appelée depuis app.js si Chart.js est disponible
            console.log('Initialisation des graphiques avec les données:', stats);
            // Implémentation des graphiques à venir
        }
    </script>
</x-app-layout>
