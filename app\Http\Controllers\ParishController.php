<?php

namespace App\Http\Controllers;

use App\Models\Parish;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ParishController extends Controller
{
    public function index(): View
    {
        $user = request()->user();
        $parishes = $user->role === 'admin'
            ? Parish::latest()->paginate(10)
            : Parish::where('id', $user->parish_id)->latest()->paginate(10);
        return view('parishes.index', compact('parishes'));
    }

    public function create(): View
    {
        abort_unless(request()->user()->role === 'admin', 403);
        return view('parishes.create');
    }

    public function store(Request $request): RedirectResponse
    {
        abort_unless($request->user()->role === 'admin', 403);
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'enable_payment_tracking' => ['nullable', 'boolean'],
            'weekly_payment_amount' => ['nullable', 'numeric', 'min:0'],
            'storage_path' => ['nullable', 'string'],
        ]);

        $validated['enable_payment_tracking'] = (bool)($validated['enable_payment_tracking'] ?? false);
        if (!isset($validated['storage_path']) || $validated['storage_path'] === '') {
            $validated['storage_path'] = 'public';
        }

        Parish::create($validated);

        return redirect()->route('parishes.index')->with('status', 'Paroisse créée.');
    }

    public function show(Parish $parish): View
    {
        $user = request()->user();
        abort_unless($user->role === 'admin' || $user->parish_id === $parish->id, 403);
        return view('parishes.show', compact('parish'));
    }

    public function edit(Parish $parish): View
    {
        abort_unless(request()->user()->role === 'admin', 403);
        return view('parishes.edit', compact('parish'));
    }

    public function update(Request $request, Parish $parish): RedirectResponse
    {
        abort_unless($request->user()->role === 'admin', 403);
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'enable_payment_tracking' => ['nullable', 'boolean'],
            'weekly_payment_amount' => ['nullable', 'numeric', 'min:0'],
            'storage_path' => ['nullable', 'string'],
        ]);

        $validated['enable_payment_tracking'] = (bool)($validated['enable_payment_tracking'] ?? false);
        if (!isset($validated['storage_path']) || $validated['storage_path'] === '') {
            $validated['storage_path'] = 'public';
        }

        $parish->update($validated);

        return redirect()->route('parishes.index')->with('status', 'Paroisse mise à jour.');
    }

    public function destroy(Parish $parish): RedirectResponse
    {
        abort_unless(request()->user()->role === 'admin', 403);
        $parish->delete();
        return redirect()->route('parishes.index')->with('status', 'Paroisse supprimée.');
    }
}
